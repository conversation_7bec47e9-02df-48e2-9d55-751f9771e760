// Test script to get products list and find valid product IDs
const axios = require('axios');

// API configuration for products
const getProducts = () => ({
  url: "/products",
  method: "GET",
  params: {
    business_id: "12483", // Development business ID
    branch_id: "",
    display_source: 2,
    attributes: 1,
  },
  timeout: 30000,
});

// Create axios instance
const axiosClient = axios.create({
  baseURL: "https://beta1.tossdown.com/api", // Development base URL
  timeout: 30000,
});

// Function to test the products API
async function testProductsAPI() {
  try {
    console.log('🚀 Testing Products API...');
    console.log(`🌐 Base URL: ${axiosClient.defaults.baseURL}`);
    
    const config = getProducts();
    console.log('📋 Request Config:', JSON.stringify(config, null, 2));
    
    const { data } = await axiosClient(config);
    
    console.log('✅ API Response Status:', data?.status);
    console.log('📊 Items Count:', data?.items?.length || 0);
    
    if (data?.status === "1" && data?.items?.length) {
      console.log('\n📝 First 5 Products:');
      data.items.slice(0, 5).forEach((product, index) => {
        console.log(`${index + 1}. ID: ${product.menu_item_id}, Name: ${product.name}, Price: ${product.price} ${product.currency}`);
      });
      
      // Test product details for the first product
      const firstProduct = data.items[0];
      console.log(`\n🔍 Testing product details for: ${firstProduct.name} (ID: ${firstProduct.menu_item_id})`);
      
      // Test product details API
      const productDetailsConfig = {
        url: "/product_details",
        method: "GET",
        params: {
          business_id: "12483",
          item_id: firstProduct.menu_item_id,
        },
        timeout: 30000,
      };
      
      const { data: detailsData } = await axiosClient(productDetailsConfig);
      console.log('📊 Product Details Response:', JSON.stringify(detailsData, null, 2));
      
    } else {
      console.log('❌ No products found or API error');
      console.log('Full response:', JSON.stringify(data, null, 2));
    }
    
  } catch (error) {
    console.error('❌ API Error:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
}

// Run the test
testProductsAPI();
