import type { Metadata } from "next";
import { generateProductMetadataFromSources } from "@/lib/metadata-utils";
import {
  fetchProductDetail,
  mapProductDetailToViewModel,
} from "@/services/product-detail-api";
import ProductDetailClient from "../clientView";
import { getProductSeo } from "@/services/seo-api";

import { stripHtml } from "@/lib/seo-utils";
import { extractProductId } from "@/lib/utils/helperFunctions";
import { callProductsDetailsAPI } from "@/lib/apiConfigs";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  try {
    // Extract product ID from the slug
    const { slug } = await params;
    const productId = extractProductIdFromSlug(slug);

    // Fetch product details to get the product name
    const productDetails = await fetchProductDetail(productId);

    if (
      productDetails &&
      productDetails.items &&
      productDetails.items.length > 0
    ) {
      const mappedProduct = mapProductDetailToViewModel(productDetails);
      if (mappedProduct) {
        // Build metadata using product JSON SEO first, then XML, then final fallbacks
        return generateProductMetadataFromSources(mappedProduct as any);
      }
    }
  } catch (error) {
    console.error("Error generating product metadata:", error);
  }

  // Fallback metadata
  return {
    title: "Product Details - EZeats",
  };
}

const Product = async ({ params }: { params: Promise<{ slug: string }> }) => {
  const { slug } = await params;
  let product = null;
  // let error = null;
  let h1 = null;

  // extract product ID from slug
  const productId = extractProductId(slug);

  // const productDetails = await fetchProductDetail(productId);
  // function to call product details API
  const { data, error } = await callProductsDetailsAPI(productId);

  return (
    <>
      {/* hidden h1 for SEO */}
      {h1 && <h1 className="sr-only">{h1}</h1>}

      <ProductDetailClient product={product} error={error} productId={slug} />
    </>
  );
};

export default Product;
