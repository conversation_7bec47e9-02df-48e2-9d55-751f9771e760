import { NextRequest, NextResponse } from "next/server";

import { callProductsDetailsAPI } from "@/lib/apiConfigs";

// get product details API route handler
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // get the product ID from params
    const { id: productId } = await params;

    if (!productId) {
      return NextResponse.json(
        { data: null, meta: null, error: "Product ID is required" },
        { status: 400 }
      );
    }

    // function to call the get product details API
    const { data, error } = await callProductsDetailsAPI(productId);

    // verify if error
    if (error) {
      // return error response
      return NextResponse.json(
        { data: null, meta: null, error },
        { status: 500 }
      );
    }

    // get the first product from the response (product details API returns array with single item)
    const product = data?.[0] || null;

    if (!product) {
      return NextResponse.json(
        { data: null, meta: null, error: "Product not found" },
        { status: 404 }
      );
    }

    // return success response with product details
    return NextResponse.json(
      { data: product, meta: null, error: null },
      { status: 200 }
    );
  } catch (error) {
    // return error response
    return NextResponse.json(
      { data: null, meta: null, error },
      { status: 500 }
    );
  }
}
