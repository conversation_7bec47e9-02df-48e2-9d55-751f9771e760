// Test script to call the product details API route
const axios = require("axios");

// Test product ID
const TEST_PRODUCT_ID = "306440";

// Function to test the API route
async function testProductDetailsRoute() {
  try {
    console.log("🚀 Testing Product Details API Route...");
    console.log(`📦 Product ID: ${TEST_PRODUCT_ID}`);

    // Test the internal API route
    const response = await axios.get(
      `http://localhost:3001/api/product-details/${TEST_PRODUCT_ID}`
    );

    console.log("✅ API Route Response Status:", response.status);
    console.log("📊 Response Data:", JSON.stringify(response.data, null, 2));

    if (response.data.data) {
      const product = response.data.data;

      console.log("\n📝 Product Details:");
      console.log("- ID:", product.id);
      console.log("- Name:", product.name);
      console.log("- Price:", product.price, product.currency);
      console.log("- Rating:", product.rating);
      console.log(
        "- Description:",
        product.description?.substring(0, 100) + "..."
      );

      console.log("\n⭐ Reviews:");
      if (product.reviews) {
        console.log("- Total Reviews:", product.reviews.total_reviews);
        console.log("- Average Rating:", product.reviews.rating);
        console.log("- Users Count:", product.reviews.users?.length || 0);

        if (product.reviews.users?.length > 0) {
          console.log("\n👤 Sample Review:");
          const review = product.reviews.users[0];
          console.log("- User:", review.user_name);
          console.log("- Rating:", review.rating);
          console.log("- Comment:", review.comment);
          console.log("- Date:", review.date);
        }
      }

      console.log("\n🔍 SEO Data:");
      if (product.seo && Object.keys(product.seo).length > 0) {
        console.log("- Title:", product.seo.title);
        console.log("- H1:", product.seo.h1);
        console.log("- Description:", product.seo.description);
        console.log("- Keywords:", product.seo.keywords);
      } else {
        console.log("- No SEO data found");
      }
    } else {
      console.log("❌ No product data found");
    }
  } catch (error) {
    console.error("❌ API Route Error:", error.message);
    if (error.response) {
      console.error("Response Status:", error.response.status);
      console.error("Response Data:", error.response.data);
    }
  }
}

// Run the test
testProductDetailsRoute();
