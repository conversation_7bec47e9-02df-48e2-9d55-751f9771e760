import { Metadata } from "next";

import Cookies from "js-cookie";
import moment from "moment-timezone";
import { XMLParser } from "fast-xml-parser";
import { v4 as uuidv4 } from "uuid";

import {
  RootXML,
  PageSEO,
  PageSEODetails,
  GenericMap,
  Locations,
  OrderTypes,
  DropDownList,
  ScheduleTypes,
  AddressDetails,
  CookiesDetails,
  BusinessInfo,
  UserDetails,
} from "@/types";
import {
  dateTimeFormats,
  xmlParserOptions,
  checkoutScripts,
  profileScripts,
} from "@/lib/utils/constants";
import { dispatch } from "@/lib/store";
import { displayToast } from "@/lib/store/slices/toast";
import { updateOrderStore } from "@/lib/store/slices/order";

// function to create Next.js metadata from SEO data
export const createMetadata = (seoDetails: PageSEODetails): Metadata => {
  return {
    title: seoDetails?.pagetitle,
    description: seoDetails?.desc,
    keywords: seoDetails?.keywords,

    openGraph: {
      title: seoDetails?.pagetitle,
      description: seoDetails?.desc,
      siteName: "EZeats",
      locale: "en_CA",
      type: "website",
    },
    twitter: {
      title: seoDetails?.pagetitle,
      description: seoDetails?.desc,
      card: "summary_large_image",
    },
    robots: {
      index: true,
      follow: true,
      nocache: false,
    },
  };
};

// function to parse XML data and extract SEO information for a specific page
export const parseXMLForSEOData = (
  data: string,
  pageName: string
): PageSEODetails | null => {
  try {
    // create an instance of the XMLParser with the specified options
    const xmlParser: XMLParser = new XMLParser(xmlParserOptions);

    // parse the XML data
    const parsedData: RootXML = xmlParser?.parse(data);

    // check if the parsed data contains an array of pages
    if (Array?.isArray(parsedData?.results?.page)) {
      // find the target page by name
      const page: PageSEO | undefined = parsedData?.results?.page?.find(
        (page: PageSEO) => page["@_name"] === pageName
      );

      // check if the page has valid SEO data
      if (page?.seo || page?.generated) {
        return {
          pagetitle: String(
            page?.seo?.pagetitle || page?.generated?.pagetitle || ""
          )?.trim(),
          h1: String(page?.seo?.h1 || page?.generated?.h1 || "")?.trim(),
          desc: String(page?.seo?.desc || page?.generated?.desc || "")?.trim(),
          keywords: String(
            page?.seo?.keywords || page?.generated?.keywords || ""
          )?.trim(),
        };
      }
    }

    // if no valid SEO data is found, return null
    return null;

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    // if there's an error parsing the XML, return null
    return null;
  }
};

// function to get the first attribute value
export const getAttributeValue = (attributes: GenericMap[]): string => {
  // get the first attribute value
  const attribute: unknown =
    attributes?.[0] && Object?.values(attributes[0])?.[0];

  // return the attribute value as string
  return attribute?.toString() || "";
};

/**
 * function to format the price with currency symbol & decimal places
 * regex to add space between currency & amount
 * handles invalid currency codes by falling back to simple concatenation
 */
export const getFormattedPrice = (
  price: number,
  currency: string,
  decimalPlaces: number
): string => {
  try {
    return new Intl.NumberFormat("en-PK", {
      style: "currency",
      currencyDisplay: "symbol",
      currency: currency,
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces,
    })
      ?.format(price || 0)
      ?.replace(/(\D)(\d)/, (_, currency, amount) => `${currency}${amount}`);

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    // fallback: return currency concatenated with formatted price
    return `${currency} ${(price || 0)?.toFixed(decimalPlaces)}`;
  }
};

// function to extract available order types from business locations
export const processOrderTypes = (
  locations: Locations[] | null
): { orderTypes: string[]; orderType: string } => {
  const orderTypes: string[] = [];
  let delivery: boolean = false;
  let pickup: boolean = false;

  // check if any branch supports delivery & pickup
  for (const location of locations ?? []) {
    // check if location supports delivery
    if (location?.delivery) {
      delivery = true;
    }

    // check if location supports pickup
    if (location?.pickup) {
      pickup = true;
    }

    // stop early if both found
    if (delivery && pickup) {
      break;
    }
  }

  // add delivery order type if available
  if (delivery) {
    orderTypes?.push(OrderTypes.DELIVERY);
  }

  // add pickup order type if available
  if (pickup) {
    orderTypes?.push(OrderTypes.PICKUP);
  }

  // return order types list and first order type as selected
  return { orderTypes, orderType: orderTypes?.[0] ?? "" };
};

// function to find nearest branch by calculating distance using Haversine formula
export const findNearestBranch = (
  userLocation: google.maps.LatLngLiteral | null,
  locations: Locations[]
): Locations | null => {
  // return if user location is not available
  if (!userLocation) {
    return null;
  }

  // filter branches that supports delivery
  const deliveryLocations: Locations[] = locations?.filter(
    (branch: Locations) => branch.delivery === 1
  );

  // set first delivery location as nearest branch
  let nearestBranch: Locations = deliveryLocations?.[0] || null;

  // calculate distance from user location to first delivery location
  let initialBranchDistance: number = calculateDistance(userLocation, {
    lat: parseFloat(deliveryLocations?.[0]?.lat),
    lng: parseFloat(deliveryLocations?.[0]?.lng),
  });

  // iterate through remaining delivery locations to find nearest one
  for (let index = 1; index < deliveryLocations?.length; index++) {
    // calculate distance from user location to current delivery location
    const branchDistance: number = calculateDistance(userLocation, {
      lat: parseFloat(deliveryLocations?.[index]?.lat),
      lng: parseFloat(deliveryLocations?.[index]?.lng),
    });

    // update nearest branch if current distance is smaller
    if (branchDistance < initialBranchDistance) {
      initialBranchDistance = branchDistance;
      nearestBranch = deliveryLocations?.[index];
    }
  }

  return nearestBranch;
};

// function to calculate distance between two points using Haversine formula
const calculateDistance = (
  destination: google.maps.LatLngLiteral,
  origin: { lat: number; lng: number }
): number => {
  // earth's radius in kilometers
  const radius: number = 6371;

  // calculate distance using Haversine formula
  const dLat: number = ((origin?.lat - destination?.lat) * Math.PI) / 180;
  const dLng: number = ((origin?.lng - destination?.lng) * Math.PI) / 180;

  // calculate distance
  const haversineFormula: number =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((destination?.lat * Math.PI) / 180) *
      Math.cos((origin?.lat * Math.PI) / 180) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);

  // calculate central angle
  const centralAngle: number =
    2 *
    Math.atan2(Math.sqrt(haversineFormula), Math.sqrt(1 - haversineFormula));

  // distance in kilometers
  return radius * centralAngle;
};

// function to filter locations by order type and return formatted dropdown list
export const getFilteredLocationsList = (
  locations: Locations[] | null,
  orderType: OrderTypes
): DropDownList[] => {
  // return empty array if locations is not available
  if (!locations) {
    return [];
  }

  return locations?.reduce(
    (locationsList: DropDownList[], location: Locations) => {
      // check if location supports the specified order type
      if (location?.[orderType] === 1) {
        locationsList?.push({
          label: `${location?.address}, ${location?.city}`,
          value: location?.id,
        });
      }

      return locationsList;
    },
    []
  );
};

// function to return time according to provided time zone offset
export const convertTimeZoneOffset = (
  timeZoneOffset: string,
  format = dateTimeFormats?.DATE
): string => {
  let currentDate: string = moment().format(format);

  // get the current date according to the time zone if exists
  if (timeZoneOffset) {
    currentDate = moment.utc().clone().utcOffset(timeZoneOffset).format(format);
  }

  return currentDate;
};

// function to generate dates for order schedule
export const generateScheduleDates = (
  timeZoneOffset: string,
  scheduleType?: string
): DropDownList[] => {
  // get the current date according to the time zone
  const formattedStartDate: string = convertTimeZoneOffset(timeZoneOffset);
  const startDate: moment.Moment = moment(formattedStartDate);

  // create an array to store the dates
  const datesList: DropDownList[] = [];

  // loop to generate dates for 7 days
  for (let day = 0; day < 7; day++) {
    datesList?.push({
      // display day name & full date for subscriptions & one-time respectively
      label:
        scheduleType === ScheduleTypes?.LATER
          ? startDate?.format(dateTimeFormats?.MONTH_DATE)
          : startDate?.format(dateTimeFormats?.DAY) + "s",
      value: startDate?.format(dateTimeFormats?.DATE),
    });

    // increment the date by 1 day
    startDate?.add(1, "days");
  }

  return datesList;
};

// function to validate order form
export const validateOrderForm = (
  orderType: string,
  addressDetails: AddressDetails,
  location: Locations | null,
  scheduleType: string
): string => {
  // verify order type
  if (!orderType) {
    return "Please select an order type";
  }

  // verify delivery details
  if (
    orderType === OrderTypes.DELIVERY &&
    (!addressDetails?.address ||
      !addressDetails?.location?.lat ||
      !addressDetails?.location?.lng)
  ) {
    return "Almost there! Just enter your address to unlock our menu";
  }

  // verify pickup location
  if (orderType === OrderTypes.PICKUP && !location) {
    return "Almost there! Just select a pickup location to unlock our menu";
  }

  // verify schedule type
  if (!scheduleType) {
    return "Almost there! Just set your schedule to unlock our menu";
  }

  return "";
};

// function to set user notification toast
export const userNotification = (message: string, type?: string): void => {
  dispatch(displayToast({ display: true, type: type || "error", message }));
};

// function to get & validate the cookies (server-side)
export const getServerCookies = (
  cookieValue?: string
): CookiesDetails | null => {
  // verify if cookie exists
  if (cookieValue) {
    try {
      return JSON.parse(cookieValue);

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      return null;
    }
  }

  return null;
};

// function to get & validate the cookie by key
export const getCookies = (key: string): CookiesDetails | null => {
  const cookieDetails: string | undefined = Cookies?.get(key);

  // verify if cookie exists
  if (cookieDetails) {
    try {
      return JSON.parse(cookieDetails);

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      return null;
    }
  }

  return null;
};

// function to remove a cookie by key
export const removeCookie = (key: string): void => {
  Cookies?.remove(key, { path: "/" });
};

// function to updates cookie by key
export const updateCookies = (
  key: string,
  details: GenericMap | UserDetails
): void => {
  // function call to get saved cookies
  const cookieDetails: CookiesDetails | null = getCookies(key);

  // set updated cookies
  Cookies?.set(key, JSON.stringify({ ...(cookieDetails ?? {}), ...details }), {
    expires: 30,
    path: "/",
    sameSite: "lax",
  });
};

// function to create cart id
export const createCartId = (): string => {
  // generate cart ID
  const cartId: string = uuidv4();

  // update cart ID in order store
  dispatch(updateOrderStore({ type: "cartId", value: cartId }));

  // return cart ID
  return cartId;
};

// function to generate URL-friendly slug from product name and ID
export const generateProductSlug = (
  productName: string,
  productId: string
): string => {
  // convert product name to lowercase and replace spaces/special chars with hyphens
  const nameSlug = productName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, "") // remove special characters except spaces and hyphens
    .replace(/\s+/g, "-") // replace spaces with hyphens
    .replace(/-+/g, "-") // replace multiple hyphens with single hyphen
    .trim(); // remove leading/trailing spaces

  // combine name slug with product ID
  return `${nameSlug}-${productId}`;
};

// function to extract product id from slug
export const extractProductId = (slug: string): string => {
  // break the slug
  const slugDetails: string[] = slug?.split("-");

  // return the product id from slug
  return slugDetails?.[slugDetails?.length - 1] || "";
};

// function to pass business info to checkout via DOM
export const passBusinessInfo = (businessInfo: BusinessInfo): void => {
  // additional safety check if business info script already exists
  const businessInfoScript: HTMLElement | null = document?.getElementById(
    checkoutScripts?.businessInfo
  );

  // remove script if exists
  if (businessInfoScript) {
    businessInfoScript?.remove();
  }

  // create new script element
  const script: HTMLScriptElement = document?.createElement("script");

  // set business info in script if element exists
  if (script) {
    script.id = checkoutScripts?.businessInfo;
    script.innerHTML = `var businessInfo = ${JSON.stringify(businessInfo)};`;
    document?.head?.appendChild(script);
  }
};

// function to load checkout css
const createLinkElement = (id: string, href: string = ""): void => {
  // create the link element if it doesn't exist
  if (!document?.getElementById(id)) {
    // create the link element
    const link: HTMLLinkElement = document?.createElement("link");

    link.id = id;
    link.rel = "stylesheet";
    link.href = href;

    document?.head?.appendChild(link);
  }
};

// function to load external scripts
const createScriptElement = (id: string, src: string = ""): Promise<void> => {
  // create a promise to load the script
  return new Promise<void>((resolve, reject) => {
    // resolve the promise if script is already loaded
    if (document?.getElementById(id)) {
      resolve();

      return;
    }

    // create the script element
    const script: HTMLScriptElement = document?.createElement("script");

    script.id = id;
    script.src = src;
    script.async = false;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error(`Failed to load ${src}`));

    document?.body?.appendChild(script);
  });
};

// function to load checkout scripts
export const integrateCheckout = async (): Promise<void> => {
  // load checkout CSS
  createLinkElement(
    checkoutScripts?.checkoutCSS,
    process.env.NEXT_PUBLIC_CHECKOUT_CSS
  );

  // load chunk js
  await createScriptElement(
    checkoutScripts?.checkoutChunk,
    process.env.NEXT_PUBLIC_CHECKOUT_CHUNK
  );

  // load main js, wait for chunk js to load to be available for main functions
  await createScriptElement(
    checkoutScripts?.checkoutMain,
    process.env.NEXT_PUBLIC_CHECKOUT_MAIN
  );
};

// function to pass business info to profile via DOM
export const passProfileInfo = (businessInfo: BusinessInfo): void => {
  // additional safety check if business info script already exists
  const businessInfoScript: HTMLElement | null = document?.getElementById(
    profileScripts?.businessInfo
  );

  // remove script if exists
  if (businessInfoScript) {
    businessInfoScript?.remove();
  }

  // create new script element
  const script: HTMLScriptElement = document?.createElement("script");

  // set business info in script if element exists
  if (script) {
    script.id = profileScripts?.businessInfo;
    script.innerHTML = `var businessInfo = ${JSON.stringify(businessInfo)};`;
    document?.head?.appendChild(script);
  }
};

// function to load profile scripts
export const integrateProfile = async (): Promise<void> => {
  // load profile CSS
  createLinkElement(
    profileScripts?.profileCSS,
    process.env.NEXT_PUBLIC_PROFILE_CSS
  );

  // load chunk js
  await createScriptElement(
    profileScripts?.profileChunk,
    process.env.NEXT_PUBLIC_PROFILE_CHUNK
  );

  // load main js, wait for chunk js to load to be available for main functions
  await createScriptElement(
    profileScripts?.profileMain,
    process.env.NEXT_PUBLIC_PROFILE_MAIN
  );
};

// function to remove DOM elements
export const removeDOMElements = (elementIds: string[]): void => {
  elementIds?.forEach((id) => {
    document?.getElementById(id)?.remove();
  });
};

// function to cleanup MUI elements
export const cleanupMUIElements = (): void => {
  // remove every element that has a class with "Mui" in it
  document
    ?.querySelectorAll('[class*="Mui"]')
    ?.forEach((element) => element?.remove());

  // reset body styles (scroll is locked by MUI Modal/Dialog)
  document.body.style.overflow = "";
  document.body.style.paddingRight = "";
};

// function to format phone number with "Eats" branding
export const formatPhoneNumber = (phoneNumber: string): string => {
  if (!phoneNumber) return "";

  // check if the phone number contains "3287"
  if (phoneNumber?.includes("3287")) {
    // replace "3287" with "Eats(3287)"
    return phoneNumber?.replace("3287", "-Eats(3287)");
  }

  return phoneNumber;
};
