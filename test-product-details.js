// Test script to call the product details API
const axios = require("axios");

// Test product ID - you can change this to any valid product ID
const TEST_PRODUCT_ID = "306440";

// API configuration
const getProductDetails = (productId) => ({
  url: "/product_details",
  method: "GET",
  params: {
    business_id: "12483", // Development business ID
    item_id: productId,
  },
  timeout: 30000,
});

// Create axios instance
const axiosClient = axios.create({
  baseURL: "https://beta1.tossdown.com/api", // Development base URL
  timeout: 30000,
});

// Function to test the API call
async function testProductDetailsAPI() {
  try {
    console.log("🚀 Testing Product Details API...");
    console.log(`📦 Product ID: ${TEST_PRODUCT_ID}`);
    console.log(`🌐 Base URL: ${axiosClient.defaults.baseURL}`);

    const config = getProductDetails(TEST_PRODUCT_ID);
    console.log("📋 Request Config:", JSON.stringify(config, null, 2));

    const { data } = await axiosClient(config);

    console.log("✅ API Response Status:", data?.status);
    console.log("📊 Items Count:", data?.items?.length || 0);

    if (data?.status === "1" && data?.items?.length) {
      const product = data.items[0];

      console.log("\n📝 Product Details:");
      console.log("- ID:", product.menu_item_id);
      console.log("- Name:", product.name);
      console.log("- Price:", product.price, product.currency);
      console.log("- Rating:", product.product_rating);
      console.log("- Description:", product.desc?.substring(0, 100) + "...");

      console.log("\n⭐ Reviews:");
      if (product.product_reviews) {
        console.log("- Total Reviews:", product.product_reviews.total_reviews);
        console.log("- Average Rating:", product.product_reviews.rating);
        console.log(
          "- Users Count:",
          product.product_reviews.users?.length || 0
        );

        if (product.product_reviews.users?.length > 0) {
          console.log("\n👤 Sample Review:");
          const review = product.product_reviews.users[0];
          console.log("- User:", review.user_name);
          console.log("- Rating:", review.rating);
          console.log("- Comment:", review.comment);
          console.log("- Date:", review.date);
        }
      }

      console.log("\n🔍 SEO Data:");
      if (product.seo) {
        console.log("- Title:", product.seo.title);
        console.log("- H1:", product.seo.h1);
        console.log("- Description:", product.seo.description);
        console.log("- Keywords:", product.seo.keywords);
      } else {
        console.log("- No SEO data found");
      }

      console.log("\n📋 Full Response Structure:");
      console.log(JSON.stringify(data, null, 2));
    } else {
      console.log("❌ No product data found or API error");
      console.log("Full response:", JSON.stringify(data, null, 2));
    }
  } catch (error) {
    console.error("❌ API Error:", error.message);
    if (error.response) {
      console.error("Response Status:", error.response.status);
      console.error("Response Data:", error.response.data);
    }
  }
}

// Run the test
testProductDetailsAPI();
